import { Form, Input, Select, Space } from 'antd'
import React from 'react'

const corpUnits = [
  {
    value: '(AT)',
    label: '(AT)'
  },
  {
    value: '(CAS)',
    label: '(CAS)'
  },
  {
    value: '(CF)',
    label: '(CF)'
  },
  {
    value: '(CH)',
    label: '(CH)'
  },
  {
    value: '(DE)',
    label: '(DE)'
  },
  {
    value: '(ES)',
    label: '(ES)'
  },
  {
    value: '(FR)',
    label: '(FR)'
  },
  {
    value: '(ITA)',
    label: '(ITA)'
  }
]

export default function ServerSoftwareName({ form, ...props }) {
  const italyComponent = form?.getFieldValue('italyComponent')

  return (
    <Space.Compact style={{ minWidth: '100%' }}>
      <Form.Item
        style={{ marginBottom: 0 }}
        className="name-input"
        name="name"
        rules={[{ required: props.required, message: '' }]}
      >
        <Input disabled={props.disabled} />
      </Form.Item>
      {italyComponent && (
        <Form.Item
          style={{ marginBottom: 0 }}
          name="suffix"
          rules={[{ required: false, message: 'Please select a suffix!' }]}
        >
          <Select disabled={props.disabled} style={{ width: '90px' }} options={corpUnits} />
        </Form.Item>
      )}
    </Space.Compact>
  )
}
