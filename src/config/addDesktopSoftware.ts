import type { DesktopSoftware, WizardFormModel, WizardStep } from '../model'
import {
  BUSINESS_AREA_CF,
  BUSINESS_AREA_CH,
  SUB_APPLICATION_OWNER_TECHNICAL,
  SUB_APPLICATION_RESPONSIBLE_FUNCTIONAL,
  USER_ID_OSCAR,
  USER_ID_RAMON
} from '../constants'
import { getFieldByName } from '../helpers'
import { useLeaniX } from '../hooks/leanix'

const {
  createFactSheet,
  addSubscription,
  addObserver,
  createAppRelation,
  getFactSheetByNameAndType,
  updateDesktopSoftwareFactsheet
} = useLeaniX()

export const addDesktopSoftwareForm: WizardFormModel = {
  id: 'addDesktopSoftware',
  name: 'Add Desktop Software',
  originFactSheet: 'ITComponent',
  addWithTemporaryMode: false,
  onlyAdmin: false,
  saveErrorMessage: 'Error while saving the desktop software. Please try again',
  saveSuccessMessage: 'Desktop Software successfully created',
  steps: [
    {
      name: 'Information',
      rules: [
        // WHEN packaged === false -> subcategory, ring, orderable
        (form, steps, currentStep, currentUser, update, deeplinkArgs) => {
          const value: boolean | undefined = form.getFieldValue('packagedByGroupIT')

          if (value !== undefined) {
            const newSteps: WizardStep[] = [...steps]
            const fieldChange0 = getFieldByName(newSteps, 0, 'packagedByGroupIT')
            const fieldChange1 = getFieldByName(newSteps, 0, 'subcategory')
            const fieldChange2 = getFieldByName(newSteps, 0, 'ring')
            const fieldChange3 = getFieldByName(newSteps, 0, 'orderable')

            if (deeplinkArgs.packagedByGroupIT != undefined) {
              fieldChange0.disabled = true
            }
            else {
              fieldChange0.disabled = false
            }

            fieldChange1.visible = value
            fieldChange2.visible = value
            fieldChange3.visible = value

            newSteps[0].fields = newSteps[0].fields.map((f) => {
              if (f.name === 'packagedByGroupIT') { return fieldChange0 }
              if (f.name === 'subcategory') { return fieldChange1 }
              if (f.name === 'ring') { return fieldChange2 }
              if (f.name === 'orderable') { return fieldChange3 }
              return f
            })

            const bAreaUser = getFieldByName(newSteps, 1, 'businessAreaUser')
            const bAreaOwner = getFieldByName(newSteps, 1, 'businessAreaOwner')

            if (value) {
              bAreaUser.visible = true
              bAreaOwner.visible = true
              bAreaOwner.disabled = true
              form.setFieldValue('businessAreaOwner', BUSINESS_AREA_CF)
              form.setFieldValue('businessAreaUser', [BUSINESS_AREA_CH])
            }
            else {
              bAreaUser.visible = false
              bAreaOwner.visible = true
              bAreaOwner.disabled = false
              form.setFieldValue('businessAreaOwner', undefined)
              form.setFieldValue('businessAreaUser', undefined)
            }
            newSteps[1].fields = newSteps[1].fields.map((f) => {
              if (f.name === 'businessAreaUser') { return bAreaUser }
              if (f.name === 'businessAreaOwner') { return bAreaOwner }
              return f
            })
            update(newSteps)
          }
        }
      ],
      fields: [
        {
          name: 'packagedByGroupIT',
          title: 'Desktop Software packaged by Group IT',
          description: 'Please provide this information',
          required: false,
          fieldType: 'Switch',
          visible: true,
          disabled: false
        },
        {
          name: 'name',
          title: 'Name',
          description: 'Display name of the desktop software (please do not specify manufacturer or version numbers)',
          required: true,
          fieldType: 'Text',
          visible: true
        },
        {
          name: 'appReference',
          title: 'Application Reference (if a suitable application exists)',
          description: 'Please provide a application reference. Based on this reference you will receive suggestions while entering the data.',
          required: false,
          fieldType: 'AppReferenceWithSuggestionDSW',
          addonBefore: undefined,
          visible: true,
          loadFactSheet: 'Application'
        },
        {
          name: 'description',
          title: 'Description',
          description: 'At least 1-2 sentences. What can be done with the software?',
          required: true,
          fieldType: 'TextArea',
          visible: true
        },
        {
          name: 'desktopSoftwareReferences',
          title: 'Desktop-Software Reference (if a suitable desktop software exists)',
          description: 'Please enter software dependencies here. For example, if a software requires Java '
            + 'in order to function smoothly, then "JVM Java Eclipse Temurin'
            + ' by Adoptium" must be entered in this field.',
          required: false,
          fieldType: 'MultiSelect',
          loadFactSheet: 'DesktopSoftware',
          visible: true
        },
        {
          name: 'subcategory',
          title: 'Subcategory',
          description: 'Please provide a subcategory',
          required: false,
          fieldType: 'SingleSelect',
          disabled: true,
          visible: true,
          loadLeanIXOptions: true
        },
        {
          name: 'guiType',
          title: 'GUI-Type',
          description: 'Please provide a GUI-Type',
          required: false,
          fieldType: 'SingleSelect',
          disabled: true,
          visible: true,
          loadLeanIXOptions: true
        },
        {
          name: 'ring',
          title: 'Ring (Mission Critical)',
          description: 'In your judgment, identify the appropriate service level and disaster recovery requirements. - This information is automatically transferred when you link to the Helvetia application.',
          required: true,
          fieldType: 'SingleSelect',
          visible: true,
          loadLeanIXOptions: true
        },
        {
          name: 'orderable',
          title: 'Can be ordered in webshop',
          description: 'Please provide if it can abe ordered in webshop',
          fieldType: 'SingleSelect',
          required: false,
          disabled: true,
          visible: true,
          loadLeanIXOptions: true
        }
      ],
      customChecks: async (form, currentUser, step) => {
        let errorMessage = ''
        const inputName = form.getFieldValue('name')

        if (inputName && inputName.length) {
          // CHECK IF NAME IS UNIQUE
          const existing = await getFactSheetByNameAndType(inputName, 'ITComponent')

          if (existing.length > 0) {
            errorMessage = `An ITComponent with the name ${inputName} already exists.`
          }
          else {
            errorMessage = ''
          }
        }
        console.log('CUSTOM CHECKS', errorMessage, errorMessage.length > 0 ? errorMessage : undefined)

        return errorMessage.length > 0 ? errorMessage : undefined
      }
    },
    {
      name: 'Business Area',
      rules: [],
      fields: [
        {
          name: 'businessAreaOwner',
          title: 'Business Area (Owner)',
          description: 'Please provide a business area owner',
          required: true,
          fieldType: 'SingleSelect',
          visible: true,
          disabled: true,
          loadFactSheet: 'UserGroup'
        },
        {
          name: 'businessAreaUser',
          title: 'Business Area (User)',
          description: 'Select the country in which the software is used.',
          fieldType: 'MultiSelect',
          required: true,
          visible: true,
          loadFactSheet: 'UserGroup'
        }
      ],
      customChecks: async (form) => {
        const businessAreaOwner = form.getFieldValue('businessAreaOwner') || []
        const businessAreaUser = form.getFieldValue('businessAreaUser') || []

        // Check if value is redundant
        for (const user of businessAreaUser) {
          if (user === businessAreaOwner) {
            return 'The Business Area Owner and Business Area User cannot be the same.'
          }
        }
        for (const owner of businessAreaOwner) {
          if (businessAreaUser.includes(owner)) {
            return 'The Business Area Owner and Business Area User cannot be the same.'
          }
        }

        return undefined
      }
    },
    {
      name: 'Lifecycle',
      rules: [],
      fields: [{
        name: 'active',
        title: 'Go-Live',
        description: 'Date since when the Desktop Software is productive and in use',
        fieldType: 'DatePicker',
        required: true,
        visible: true
      }],
      customChecks: async () => {
        return undefined
      }
    },
    {
      name: 'Subscriptions',
      rules: [],
      fields: [
        {
          name: 'applicationOwners',
          title: 'Application Owner (technical)',
          description: 'The Application Owner (technical) is responsible for maintenance, further development rsp. configuration as well as for the 2nd level support of the relevant application. He ensures that '
            + 'the business and technical requirements are implemented and that a reliable and costefficient '
            + 'operation is ensured in accordance with the business requirements and agreements.',
          fieldType: 'UserSelectMultiple',
          required: true,
          visible: true
        },
        {
          name: 'applicationResponsibles',
          title: 'Application Responsible (functional)',
          description: 'The Application Responsible (functional) assumes functional responsibility for the application. '
            + 'This includes the definition and prioritization of requirements for the further development '
            + 'and improvements of the application. The definition of the security requirements, the rules '
            + 'for assigning access authorizations, and the assessment of business criticality are also his '
            + 'responsibility.',
          fieldType: 'UserSelectMultiple',
          required: false,
          visible: true
        }
      ],
      customChecks: async () => {
        return undefined
      }
    }
  ],
  init: (form, currentUser, transferData, factSheetId, validate, deepLinkArgs) => {
    if (deepLinkArgs?.packagedByGroupIT !== undefined) {
      form.setFieldValue('packagedByGroupIT', deepLinkArgs.packagedByGroupIT)
    }
    else {
      form.setFieldValue('packagedByGroupIT', true)
    }

    // SET DEFAULTS:
    form.setFieldValue('orderable', 'yes')
    form.setFieldValue('guiType', 'guitype1')
    form.setFieldValue('subcategory', 'subcategory3')
    form.setFieldValue('businessAreaOwner', BUSINESS_AREA_CF)
    form.setFieldValue('businessAreaUser', [BUSINESS_AREA_CH])
  },
  save: async (fsData, currentUser) => {
    // console.log("SAVE desktop software", fsData);

    let compId = ''

    try {
      const data = await createFactSheet(fsData.name, 'ITComponent', false)

      if (data?.createFactSheet?.factSheet?.id) {
        compId = data.createFactSheet.factSheet.id

        const component = await updateDesktopSoftwareFactsheet(compId, fsData as DesktopSoftware)
        // console.log("AFTER UPDATE", component);

        let relationAdded = false

        if (fsData.appReference && fsData.appReference.length > 0) {
          // NUR RELATION APP
          const relations = await createAppRelation(compId, fsData.appReference)

          if (relations.result) {
            relationAdded = true
          }
        }

        // Helper function to check if an ID is in either of the arrays
        function isPersonSubscribed(id: string, owners: string[], responsibles: string[]): boolean {
          return owners.includes(id) || responsibles.includes(id)
        }

        const componentOwners: string[] = fsData.applicationOwners || []
        const componentResponsibles: string[] = fsData.applicationResponsibles || []

        // Process each role for subscription
        for (const owner of componentOwners) {
          if (componentResponsibles.includes(owner)) {
            // If person is both owner and responsible
            await addSubscription(compId, owner, [SUB_APPLICATION_OWNER_TECHNICAL, SUB_APPLICATION_RESPONSIBLE_FUNCTIONAL])
          }
          else {
            // If person is only an owner
            await addSubscription(compId, owner, [SUB_APPLICATION_OWNER_TECHNICAL])
          }
        }

        for (const responsible of componentResponsibles) {
          if (!componentOwners.includes(responsible)) {
            // If person is only a responsible
            await addSubscription(compId, responsible, [SUB_APPLICATION_RESPONSIBLE_FUNCTIONAL])
          }
        }

        // IDs of Ramon and Oscar
        const ramonId = USER_ID_RAMON
        const oscarId = USER_ID_OSCAR

        console.log('FS DATA', fsData.packagedByGroupIT)
        if (fsData.packagedByGroupIT) {
          // Add observers for Ramon if not already present in either role
          if (!isPersonSubscribed(ramonId, componentOwners, componentResponsibles)) {
            const subRamon = await addObserver(compId, ramonId)
            console.log('ADD 1', subRamon)
          }
          // Add observers for Oscar if not already present in either role
          if (!isPersonSubscribed(oscarId, componentOwners, componentResponsibles)) {
            const subOscar = await addObserver(compId, oscarId)
            console.log('ADD 2', subOscar)
          }
        }
      }
    }
    catch (e) {
      console.log('ERROR', e)

      if (compId) {
        lx.showToastr('error', 'Error while creating subscriptions. Please contact us for help.')
      }
      else {
        lx.showToastr('error', 'Error while creating desktop software. Please try it again or contact us for help.')
      }
      return undefined
    }
    return compId
  }
}
